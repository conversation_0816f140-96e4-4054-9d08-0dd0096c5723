# 完整抽獎系統測試指南

## 系統概述

我已經完成了完整的抽獎系統實現，包括：
- ✅ 真實抽獎邏輯
- ✅ gameRecordId 獲取和設置
- ✅ 抽獎 API 結果正確處理
- ✅ 抽獎記錄查詢和搜索
- ✅ 管理後台功能

## 測試前準備

### 1. 數據庫準備
```sql
-- 確保有可用的 coupon
INSERT INTO "Coupon" (id, code, "createdAt", "updatedAt") 
VALUES 
  ('coupon1', 'WIN001', NOW(), NOW()),
  ('coupon2', 'WIN002', NOW(), NOW()),
  ('coupon3', 'WIN003', NOW(), NOW());

-- 設置抽獎機率
UPDATE "WebsiteConfig" SET "luckyDrawChance" = 0.5 WHERE id = (
  SELECT id FROM "WebsiteConfig" ORDER BY "createdAt" DESC LIMIT 1
);
```

### 2. 檢查 API 端點
- `POST /api/record/new` - 創建遊戲記錄
- `POST /api/game/draw` - 執行抽獎
- `GET /api/admin/draw-records` - 查詢抽獎記錄
- `GET /api/coupon/my` - 查詢用戶 coupon

## 完整測試流程

### 階段 1: 遊戲和記錄創建
1. **訪問遊戲頁面**: `/game`
2. **輸入暱稱**: 填寫用戶暱稱
3. **選擇遊戲**: 選擇任一遊戲類型
4. **完成遊戲**: 獲得分數
5. **驗證記錄創建**: 
   - 檢查 console 是否有 gameRecordId
   - 確認數據庫中有新的 GameRecord

### 階段 2: 抽獎功能測試
1. **點擊抽獎按鈕**: "分享立即抽"
2. **觀察狀態變化**: 
   - 按鈕顯示 "抽獎中..."
   - 按鈕被禁用
3. **檢查結果**:
   - 顯示中獎或未中獎對話框
   - 如果中獎，顯示 PIN 碼
4. **驗證數據庫**:
   - GameRecord 的 drawResult 被設置
   - 如果中獎，couponId 被設置
   - Coupon 的 userId 被設置

### 階段 3: 重複抽獎測試
1. **關閉對話框**
2. **再次點擊抽獎**: 應該顯示 "已經抽過獎了"
3. **驗證防重複**: 確保不能重複抽獎

### 階段 4: 管理後台測試
1. **訪問管理後台**: `/game-control-dashboard`
2. **查看抽獎記錄**:
   - 驗證記錄顯示正確
   - 測試日期範圍過濾
   - 測試遊戲類型過濾
   - 測試抽獎結果過濾
3. **搜索功能測試**:
   - 搜索用戶暱稱
   - 搜索 PIN 碼
   - 搜索 Email
4. **導出功能**: 測試 CSV 導出

## 預期結果驗證

### 中獎情況
```json
// API 響應
{
  "result": "win",
  "coupon": {
    "code": "WIN001"
  }
}

// 前端狀態
- drawResult: DrawResult.Win
- couponCode: "WIN001"
- 顯示中獎對話框
```

### 未中獎情況
```json
// API 響應
{
  "result": "lose",
  "coupon": null
}

// 前端狀態
- drawResult: DrawResult.Lose
- couponCode: ""
- 顯示未中獎對話框
```

### 數據庫變化
```sql
-- GameRecord 表
SELECT id, "drawResult", "couponId" FROM "GameRecord" 
WHERE id = 'your-game-record-id';

-- Coupon 表（如果中獎）
SELECT id, code, "userId" FROM "Coupon" 
WHERE "userId" = 'your-user-id';
```

## 錯誤情況測試

### 1. 無效 gameRecordId
```javascript
// 手動測試
fetch('/api/game/draw', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ gameRecordId: 'invalid-id' })
});
// 預期: 404 錯誤
```

### 2. 重複抽獎
```javascript
// 對同一個 gameRecordId 發送兩次請求
// 預期: 第二次返回 "已經抽過獎了"
```

### 3. 沒有可用 coupon
```sql
-- 清空所有可用 coupon
UPDATE "Coupon" SET "userId" = 'dummy-user' WHERE "userId" IS NULL;
-- 然後嘗試中獎，應該仍然記錄為 WIN 但沒有分配 coupon
```

## 性能測試

### 1. 並發抽獎
- 多個用戶同時抽獎
- 驗證 coupon 分配的原子性

### 2. 大量數據查詢
- 創建大量抽獎記錄
- 測試查詢和搜索性能

## 監控和日誌

### 檢查 Console 日誌
```javascript
// 成功創建記錄
console.log("mutate");

// 抽獎錯誤
console.error("Draw error:", error);

// API 錯誤
console.error("Error performing draw:", error);
```

### 檢查網絡請求
1. 開發者工具 → Network 標籤
2. 驗證請求格式和響應
3. 檢查狀態碼和響應時間

## 故障排除

### 常見問題
1. **抽獎按鈕被禁用**: 檢查 gameRecordId 是否正確設置
2. **API 錯誤**: 檢查 Content-Type 標頭
3. **結果不顯示**: 檢查 result 值是否為 "win"/"lose"
4. **重複抽獎**: 檢查 drawResult 字段是否已設置

### 調試步驟
1. 檢查瀏覽器 Console
2. 檢查 Network 請求
3. 檢查數據庫狀態
4. 檢查服務器日誌

## 成功標準

✅ 遊戲記錄正確創建並返回 ID
✅ 抽獎 API 正確處理請求和響應
✅ 前端正確顯示抽獎結果
✅ 數據庫狀態正確更新
✅ 防重複抽獎機制有效
✅ 管理後台查詢功能正常
✅ 搜索和過濾功能正常
✅ 錯誤處理機制有效
