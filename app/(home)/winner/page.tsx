"use client";
import { FullScreenImage } from "@/app/components/full-screen";
import { imageUrl } from "@/utils/image-url";
import Image from "next/image";
import clsx from "clsx";

const WinnerPage = () => {
  return (
    <>
      <FullScreenImage src={imageUrl("/screen-cm.png")} />
      <div className="flex flex-col items-center relative pt-[35vw] text-center">
        <div
          className={clsx(
            "w-[76vw] h-[116vw] flex flex-col items-center pt-[7.5vw] pb-[10vw] overflow-auto",
          )}
        >
          <Image
            className="w-[50vw] mb-[12vw]"
            src={imageUrl("/prize-1st-title.png")}
            alt=""
            unoptimized
            width={495}
            height={188}
          />

          <div className="w-full relative">
            <div className="text-[#fff100] font-[1000] text-[5.3vw] leading-tight mb-[3vw]">
              王＊家
              <br />
              0910***179
            </div>
            <Image
              unoptimized
              className="w-[32vw] absolute right-[4vw] top-[-3.5vw]"
              src={imageUrl("/prize-1st.png")}
              width={344}
              height={382}
              alt=""
            />
          </div>
        </div>
      </div>
    </>
  );
};

export default WinnerPage;
