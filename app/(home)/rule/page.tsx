"use client";
import { FullScreenImage } from "@/app/components/full-screen";
import { imageUrl } from "@/utils/image-url";
import { ContentArea } from "../../components/content-area";

const RulePage = () => {
  return (
    <>
      <FullScreenImage src={imageUrl("/screen-page.png")} />
      <div className="flex flex-col items-center relative pt-[35vw]">
        <ContentArea className="pt-[3vw] pb-[2vw] px-[6.6vw]">
          <div className="font-[700] text-[3vw] mb-[3vw] leading-[6.2vw]">
            <div>
              <p className="text-[#fff100]">●活動期間</p>
              <p>2025/7/7(一) 10:00~2025/8/10(日) 23:59</p>
            </div>

            <div className="mt-[2vw]">
              <p className="text-[#fff100]">●活動方式</p>
              <p>
                活動期間至【威金森超激夏祭活動】活動網站參加趣味小遊戲，就有機會獲得威金森碳酸水兌換碼！成功闖過三關，還能參加抽獎，贏得PS5
                PRO大獎！
              </p>
              <p>【極限平衡】</p>
              <p>
                限時1分鐘，左右移動控制威金森碳酸水，讓上方滾動的瓶蓋維持平衡，堅持越久積分越高。
              </p>
              <p>【威風接招】</p>
              <p>
                限時1分鐘，移動下方威金森LOGO，接住威金森品牌相關元素，接越多積分越高。
              </p>
              <p>【威金森考驗】</p>
              <p>
                資深威粉來喊聲，挑戰經典威金森問答！每關限時10秒，共5小關，答對一題得20分，答錯則倒扣10分。
              </p>
            </div>
          </div>
        </ContentArea>
      </div>
    </>
  );
};

export default RulePage;
