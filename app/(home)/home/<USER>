import { imageUrl } from "@/utils/image-url";
import { FullScreenImage } from "../../components/full-screen";
import Link from "next/link";
import { AppButton } from "../../components/buttons/app-button";
import { DialogOpenInBrowser } from "../../components/dialog-open-in-browser";

const StartPage = () => {
  return (
    <>
      <FullScreenImage src={imageUrl("/screen-start.png")} />
      <div className="flex justify-center pt-[80vw]">
        <Link
          href="/game"
          className="relative z-0 w-[39vw] aspect-[3] active:opacity-80 flex justify-center items-center cursor-pointer"
        >
          <AppButton as="div">點擊畫面開始</AppButton>
        </Link>
      </div>
      <DialogOpenInBrowser />
    </>
  );
};

export default StartPage;
