"use client";
import { FullScreenImage } from "@/app/components/full-screen";
import { imageUrl } from "@/utils/image-url";

const CommercialPage = () => {
  return (
    <>
      <FullScreenImage src={imageUrl("/screen-cm.png")} />
      <div className="flex flex-col items-center relative pt-[35vw]">
        <div className="w-[76vw] h-[116vw] flex flex-col items-center pt-[9vw] pb-[5vw] overflow-auto">
          <h1 className="text-[#d71d24] font-[1000] text-[4.7vw] mb-[3vw]">
            WILKINSON 品牌影片
          </h1>

          <iframe
            className="w-[68vw] h-[45vw]"
            src="https://www.youtube.com/embed/5Ya-VG7gaLs"
            title="YouTube video player"
            allowFullScreen
          />
        </div>
      </div>
    </>
  );
};

export default CommercialPage;
