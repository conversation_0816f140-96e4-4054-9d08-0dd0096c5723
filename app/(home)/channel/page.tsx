"use client";
import {
  ButtonBase,
  ButtonBaseProps,
} from "@/app/components/buttons/button-base";
import { FullScreenImage } from "@/app/components/full-screen";
import { imageUrl } from "@/utils/image-url";
import Image from "next/image";

const ChannelButton = (
  props: Omit<ButtonBaseProps, "sizeClass" | "backgroundImage">,
) => {
  const { children, ...restProps } = props;
  return (
    <ButtonBase
      sizeClass="w-[33vw] aspect-[2.3]"
      backgroundImage={{
        src: imageUrl("/button-buy.png"),
        width: 498,
        height: 297,
        sizeClass: "min-w-[140%]",
      }}
      {...restProps}
    >
      {children}
    </ButtonBase>
  );
};

const ChannelPage = () => {
  const channelClass =
    "font-[1000] text-[#fff100] text-[2.5vw] py-[1.5vw] border-y-1 border-gray-500 tracking-[0.5vw] relative";
  return (
    <>
      <FullScreenImage src={imageUrl("/screen-channel.png")} />
      <div className="flex flex-col items-center relative pt-[35vw]">
        <div className="w-[72vw] h-[116vw] flex flex-col pt-[5vw] pb-[5vw] overflow-x-hidden overflow-auto text-center">
          <h1 className="font-[1000] text-[5vw] mb-[4vw] italic">購買通路</h1>
          <div className={channelClass}>● 網購通路</div>

          <div className="relative z-10 flex gap-[4vw] justify-center py-[2vw]">
            <ChannelButton>
              <Image
                unoptimized
                className="w-[25vw]"
                src={imageUrl("/button-logo-pchome.png")}
                alt=""
                width={266}
                height={51}
              />
            </ChannelButton>
            <ChannelButton>
              <Image
                unoptimized
                className="w-[25vw]"
                src={imageUrl("/button-logo-momo.png")}
                alt=""
                width={266}
                height={93}
              />
            </ChannelButton>
          </div>

          <div className={channelClass}>● 實體通路</div>
          <div className="font-bold text-[3.7vw] pl-[3vw] mt-[3.9vw] text-left flex flex-col gap-[6vw]">
            <div>
              <span className="mr-[5vw]">威金森寶特瓶</span>
              <span>全家、家樂福、唐吉軻德</span>
            </div>
            <div>
              <span className="mr-[5vw]">威金森易開罐</span>
              <span>全聯、唐吉軻德</span>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default ChannelPage;
