"use client";
import { FullScreenImage } from "@/app/components/full-screen";

import { AppButton } from "@/app/components/buttons/app-button";
import { imageUrl } from "@/utils/image-url";
import Image from "next/image";
import clsx from "clsx";
import { ContentArea } from "../../../components/content-area";

const mockData: string[] = ["123456", "234567", "345678"];

const PersonalPage = () => {
  return (
    <>
      <FullScreenImage src={imageUrl("/screen-page.png")} />
      <div className="flex flex-col items-center relative pt-[35vw] text-center">
        <ContentArea className="h-[116vw] pt-[5vw] pb-[10vw]">
          <div className="text-[#fff100] font-[1000] text-[5.3vw] leading-tight mb-[3vw]">
            恭喜獲得
            <br />
            超激夏祭り參加獎
            <span className="inline-block mx-[-0.25em]">！</span>
          </div>

          <div className="font-[900] text-[4vw] mb-[3vw]">
            憑PIN碼至全台全家門市
            <br />
            即可兌換 威金森碳酸水PET500ml 1瓶
          </div>

          <div className="flex flex-col gap-[5vw] mb-[6vw]">
            {mockData.map((code) => {
              return (
                <div
                  key={code}
                  className={clsx(
                    "bg-center bg-contain bg-no-repeat",
                    "aspect-[685/138] w-[64vw]",
                    "flex items-center px-[4vw]",
                  )}
                  style={{
                    backgroundImage: `url(${imageUrl("/voucher-background.png")})`,
                  }}
                >
                  <Image
                    unoptimized
                    className="h-[11vw] w-auto mr-[3vw]"
                    src={imageUrl("/voucher-bottle.png")}
                    alt=""
                    width={40}
                    height={122}
                  />
                  <span className="font-[500] text-[5vw] mr-[6vw]">
                    PIN碼編號
                  </span>
                  <span className="font-[900] text-[5vw]">{code}</span>
                </div>
              );
            })}
          </div>

          <a href="https://nevent.family.com.tw/fami_pin/" target="_blank">
            <AppButton as="div">如何兌換</AppButton>
          </a>
        </ContentArea>
      </div>
    </>
  );
};

export default PersonalPage;
