"use client";
import { useEffect, useMemo, useState } from "react";
import { BalanceGame, BalanceGameState } from "./balance-game";
import { BalanceGameDebugView } from "./debug-view";
import { imageUrl } from "@/utils/image-url";
import Image from "next/image";
import clsx from "clsx";
import "./range-input.scss";

const debug = false;

const formatTime = (time: number) => {
  const seconds = Math.floor(time / 1000);
  const milliseconds = Math.floor((time % 1000) / 10);
  return `${seconds.toString().padStart(2, "0")}:${milliseconds.toString().padStart(2, "0")}`;
};

export const BalanceGameApp = ({
  onGameEnd,
}: {
  onGameEnd: (score: number) => void;
}) => {
  const [data, setData] = useState<BalanceGameState>();

  const gameApp = useMemo(() => {
    return new BalanceGame({
      onUpdateUi: setData,
      onGameEnd: onGameEnd,
    });
  }, [onGameEnd]);

  useEffect(() => {
    gameApp.start();
  }, [gameApp]);

  if (debug) {
    return (
      <BalanceGameDebugView
        gameState={data}
        onUpdateUserPosition={gameApp.onUpdatePlayerPosition}
      />
    );
  }

  const {
    force = 0,
    passedTime = 0,
    position = 0,
    player = {
      position: 0,
      force: 0,
    },
  } = data ?? {};

  const rotateStyle = { transform: `rotateZ(${force * 5}deg)` };

  const isOut = Math.abs(position) > 100;

  return (
    <div className="w-full flex flex-col items-center">
      <div className="relative w-[70vw] h-[11vw] mt-[5.5vw]">
        <Image
          unoptimized
          alt=""
          src={imageUrl("/game-score-board.png")}
          width={756}
          height={121}
        />
        <div
          className="text-[5.5vw] font-bold"
          style={{ fontFamily: "MStiffHeiHK-UltraBold" }}
        >
          <div className="absolute top-[3.9vw] left-[19vw]">
            {formatTime(passedTime)}
          </div>
          <div className="absolute top-[3.9vw] left-[57vw]">
            {Math.floor(passedTime / 1000)}
          </div>
        </div>
      </div>

      <div className="mt-[7.5vw]">
        <div
          className={clsx("relative top-0 transition-all", {
            "top-[20vw]": isOut,
          })}
        >
          <div
            className="w-[60vw] flex flex-col items-center"
            style={rotateStyle}
          >
            <div
              className="w-[15vw] h-[15vw]"
              style={{
                transform: `translateX(calc(${position}/200 * 60vw)) rotateZ(${position * 2}deg)`,
              }}
            >
              <Image
                unoptimized
                alt=""
                src={imageUrl("/game-balance-cap.png")}
                width={180}
                height={180}
              />
            </div>
          </div>
        </div>

        <div
          className="w-[60vw] flex flex-col items-center"
          style={rotateStyle}
        >
          <div className="w-full rounded-full w-[70vw] h-[1vw] bg-white border-b-2 border-gray-400" />
        </div>

        <div className="w-full touch-none flex flex-col items-center mt-[5vw]">
          <div
            className="relative z-0 w-[20vw] h-[60vw]"
            style={{
              transform: `translateX(calc(${player.position}/200 * 60vw))`,
            }}
          >
            <Image
              unoptimized
              alt=""
              src={imageUrl("/game-balance-direction.png")}
              width={523}
              height={327}
              className="z-10 min-w-[40vw] absolute top-76/100 left-1/2 -translate-x-1/2 -translate-y-1/2"
            />

            <Image
              unoptimized
              alt=""
              src={imageUrl("/game-balance-water.png")}
              width={251}
              height={757}
              className="z-10 min-w-[25vw] absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 pr-[7%]"
            />
          </div>
          <input
            className="relative z-10 top-1/2 min-w-[75vw] bottom-[15vw]"
            style={{ margin: "0 -12.5vw", opacity: 0.001 }}
            type="range"
            min="-100"
            max="100"
            onChange={(e) => {
              const value = Number(e.target.value ?? 0);
              gameApp.onUpdatePlayerPosition(value);
            }}
          />
        </div>
      </div>
    </div>
  );
};
