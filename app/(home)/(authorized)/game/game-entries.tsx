"use client";
import { imageUrl } from "@/utils/image-url";
import Image from "next/image";
import { ComingSoonDialog } from "./coming-soon-dialog";
import { GameId } from "@/app/constants";

const config: {
  gameId: GameId;
  playNowImage: string;
  playAgainImage: string;
  comingSoonImage: string;
}[] = [
  {
    gameId: "balance",
    playNowImage: "/btn-game1-play-now.png",
    playAgainImage: "/btn-game1-play-again.png",
    comingSoonImage: "/btn-game1-play-now.png",
  },
  {
    gameId: "catch",
    playNowImage: "/btn-game2-play-now.png",
    playAgainImage: "/btn-game2-play-again.png",
    comingSoonImage: "/btn-game2-coming-soon.png",
  },
  {
    gameId: "quiz",
    playNowImage: "/btn-game3-play-now.png",
    playAgainImage: "/btn-game3-play-again.png",
    comingSoonImage: "/btn-game3-coming-soon.png",
  },
];

interface GameStatus {
  available: boolean;
  played: boolean;
}

export type GameState = {
  [gameId: string]: GameStatus;
};

export const GameEntries = ({
  gameState = {},
  onEntryClick,
}: {
  gameState?: GameState;
  onEntryClick: (gameId: GameId) => void;
}) => {
  const dialogId =
    config.find(({ gameId }) => !gameState[gameId]?.available)?.gameId ??
    "all-games-available";

  return (
    <>
      <div className="flex flex-col gap-[1vw]">
        {config.map(
          ({ gameId, playNowImage, playAgainImage, comingSoonImage }) => {
            const state = gameState[gameId] ?? {};
            const { available, played } = state;

            if (!available) {
              return (
                <Image
                  key={gameId}
                  alt=""
                  unoptimized
                  className="w-[70vw]"
                  width={757}
                  height={353}
                  src={imageUrl(comingSoonImage)}
                />
              );
            }

            return (
              <button key={gameId} onClick={() => onEntryClick(gameId)}>
                <Image
                  alt=""
                  unoptimized
                  className="w-[70vw] active:opacity-70"
                  width={757}
                  height={353}
                  src={
                    played ? imageUrl(playAgainImage) : imageUrl(playNowImage)
                  }
                />
              </button>
            );
          },
        )}
      </div>
      <ComingSoonDialog dialogId={dialogId} />
    </>
  );
};
