import { AppButton } from "@/app/components/buttons/app-button";
import { WideButton } from "@/app/components/buttons/wide-button";
import { DialogBase } from "@/app/components/dialog-base";
import { imageUrl } from "@/utils/image-url";
import { DrawResult } from "@prisma/client";
import Image from "next/image";
import Link from "next/link";

export const DrawResultDialog = ({
  drawResult,
  code = "XXXX",
  onClose,
  onPlayAgain,
}: {
  drawResult?: DrawResult;
  code?: string;
  onClose: () => void;
  onPlayAgain: () => void;
}) => {
  if (!drawResult) {
    return null;
  }

  const renderWinContent = () => {
    return (
      <>
        <div className="text-[9vw] text-[#fff100] font-[1000] mb-[1vw]">
          恭喜獲獎<span className="inline-block mx-[-0.25em]">！</span>
        </div>
        <div className="text-[4vw] font-[1000] mb-[1.5vw]">
          超激夏祭り<span className="inline-block mx-[-0.5em]">【參加獎】</span>
        </div>

        <div className="text-[3.5vw] font-bold text-[#d71d24] mb-[2vw]">
          威金森碳酸水 500ml 1瓶
        </div>

        <Image
          src={imageUrl("/game-catch-asset-3.png")}
          alt=""
          unoptimized
          unselectable="on"
          className="w-[12vw] mb-[1.5vw]"
          width={589}
          height={190}
        />

        <div className="text-[2.8vw] font-bold">
          ※兌換方式請參考個人頁面說明
        </div>

        <div className="border-1 border-[#d71d24] rounded-[1vw] my-[1.5vw] px-[3vw] py-[0.8vw] w-[40vw]">
          <span className="text-[4.5vw] font-[700]">PIN碼 {code}</span>
        </div>

        <Link href="/personal">
          <WideButton as="div">前往個人頁面</WideButton>
        </Link>
      </>
    );
  };

  const renderLoseContent = () => {
    return (
      <>
        <div className="text-[9vw] text-[#fff100] font-[1000] mt-[14vw] mb-[1vw]">
          殘念<span className="inline-block mx-[-0.25em]">！</span>未中獎
        </div>
        <div className="text-[4vw] font-[1000] mb-[3vw]">補充強氣 再接再厲</div>
        <AppButton onClick={onPlayAgain}>再玩一次</AppButton>
      </>
    );
  };

  return (
    <DialogBase visible>
      <div className="w-[79vw] aspect-[851/1262] relative z-0 flex flex-col items-center text-center pt-[14vw]">
        <Image
          src={
            drawResult === DrawResult.WIN
              ? imageUrl("/popup-luckydraw-reward.png")
              : imageUrl("/popup-normal.png")
          }
          alt=""
          className="absolute -z-10 top-0 left-0 w-full ml-[1.3vw]"
          unoptimized
          unselectable="on"
          width={851}
          height={1262}
        />
        {drawResult === DrawResult.LOSE
          ? renderWinContent()
          : renderLoseContent()}
        <button
          className="absolute top-0 right-0 w-[9vw] h-[9vw]"
          onClick={onClose}
        />
      </div>
    </DialogBase>
  );
};
