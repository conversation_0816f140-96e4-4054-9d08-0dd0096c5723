import { DialogButton } from "@/app/components/buttons/dialog-button";
import { Checkbox } from "@/app/components/checkbox";
import { DialogBase } from "@/app/components/dialog-base";
import { GameId } from "@/app/constants";
import { imageUrl } from "@/utils/image-url";
import Image from "next/image";
import { useEffect, useState } from "react";

const DISMISSED_DIALOG = "DISMISSED_DIALOG";

const getDialogConfig = () => {
  try {
    return JSON.parse(localStorage.getItem(DISMISSED_DIALOG) ?? "{}");
  } catch {
    return {};
  }
};

type DialogType = GameId | "all-games-available";

const config: {
  [key in DialogType]?: {
    dialogTitleImage: string;
    availableData: string;
  };
} = {
  catch: {
    dialogTitleImage: "/dialog-title-catch.png",
    availableData: "Ｏ月Ｏ日",
  },
  quiz: {
    dialogTitleImage: "/dialog-title-quiz.png",
    availableData: "Ｏ月Ｏ日",
  },
  ["all-games-available"]: {
    dialogTitleImage: "/dialog-title-all.png",
    availableData: "",
  },
};

export const ComingSoonDialog = ({ dialogId }: { dialogId: DialogType }) => {
  const [visible, setVisible] = useState(false);

  const [shouldRemember, setShouldRemember] = useState(false);

  const onClose = () => {
    setVisible(false);
    if (dialogId && shouldRemember) {
      neverShowDialog(dialogId);
    }
  };
  const neverShowDialog = (dialogId: string) => {
    const dismissedDialogConfig = getDialogConfig();
    localStorage.setItem(
      DISMISSED_DIALOG,
      JSON.stringify({ ...dismissedDialogConfig, [dialogId]: true }),
    );
  };

  useEffect(() => {
    const dismissedDialogConfig = getDialogConfig();
    if (dialogId && !dismissedDialogConfig[dialogId]) {
      setVisible(true);
    }
  }, [dialogId]);

  if (!dialogId) {
    return null;
  }

  const currentConfig = config[dialogId];

  if (!visible || !currentConfig) {
    return null;
  }

  const { dialogTitleImage, availableData } = currentConfig;

  return (
    <DialogBase visible={visible}>
      <div className="w-[79vw] aspect-[851/1262] relative z-0 flex flex-col items-center text-center pt-[12vw]">
        <Image
          src={imageUrl("/popup-game-common.png")}
          alt=""
          className="absolute -z-10 top-0 left-0 w-full h-full ml-[1.3vw]"
          unoptimized
          unselectable="on"
          width={851}
          height={1262}
        />

        <div className="w-[58vw] h-[42vw] flex flex-col justify-center">
          <Image
            src={dialogTitleImage}
            alt=""
            unoptimized
            unselectable="on"
            className="w-[58vw] min-h-[20vw] object-contain"
            width={625}
            height={300}
          />
          {availableData && (
            <div className="text-[4.8vw] font-bold tracking-tight">
              將於<span className="text-[#ffff00]">{availableData}</span>
              開放
            </div>
          )}
        </div>
        <div className="text-[5.5vw] font-bold">
          完成3關小遊戲 抽
          <span className="text-[#ffff00] tracking-tighter">PS5 PRO</span>
        </div>
        <div className="text-[4.7vw] font-bold">
          {dialogId === "all-games-available"
            ? "威粉快來挑戰!!"
            : "記得回來挑戰!!"}
        </div>

        <Checkbox checked={shouldRemember} onChange={setShouldRemember}>
          <span className="font-bold text-[3.3vw] tracking-tight">
            不再顯示此訊息
          </span>
        </Checkbox>

        <div className="mt-[5.5vw]">
          <DialogButton onClick={onClose}>關閉</DialogButton>
        </div>

        <button
          className="absolute top-0 right-0 w-[9vw] h-[9vw]"
          onClick={onClose}
        />
      </div>
    </DialogBase>
  );
};
