interface CouponRecord {
  id: string;
  code: string;
  createdAt: string;
  updatedAt: string;
  status: "available" | "assigned";
  user?: {
    id: string;
    nickname?: string;
    email?: string;
  };
}

interface PaginationInfo {
  current: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

interface StatsInfo {
  total: number;
  available: number;
  assigned: number;
}

interface CouponsResponse {
  data: CouponRecord[];
  pagination: PaginationInfo;
  stats: StatsInfo;
}

interface CouponsParams {
  page?: number;
  pageSize?: number;
  search?: string;
  status?: string;
}

interface CreateCouponsRequest {
  coupons: string[];
}

interface CreateCouponsResponse {
  message: string;
  created: number;
  duplicates: string[];
  total: number;
}

export const couponService = {
  // 獲取獎券列表
  getCoupons: async (params: CouponsParams = {}): Promise<CouponsResponse> => {
    const searchParams = new URLSearchParams();
    
    if (params.page) searchParams.set("page", params.page.toString());
    if (params.pageSize) searchParams.set("pageSize", params.pageSize.toString());
    if (params.search) searchParams.set("search", params.search);
    if (params.status) searchParams.set("status", params.status);

    const response = await fetch(`/api/admin/coupons?${searchParams}`);
    
    if (!response.ok) {
      throw new Error("獲取獎券列表失敗");
    }
    
    return response.json();
  },

  // 批量創建獎券
  createCoupons: async (data: CreateCouponsRequest): Promise<CreateCouponsResponse> => {
    const response = await fetch("/api/admin/coupons", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || "創建獎券失敗");
    }

    return response.json();
  },
};

export type {
  CouponRecord,
  PaginationInfo,
  StatsInfo,
  CouponsResponse,
  CouponsParams,
  CreateCouponsRequest,
  CreateCouponsResponse,
};
