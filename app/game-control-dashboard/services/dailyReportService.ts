interface WinnerRecord {
  id: string;
  code: string;
  createdAt: string;
  user?: {
    id: string;
    nickname?: string;
    email?: string;
  };
}

interface DailyWinnersParams {
  date: string; // YYYY-MM-DD format
}

export const dailyReportService = {
  // 獲取每日中獎清單
  getDailyWinners: async (
    params: DailyWinnersParams,
  ): Promise<WinnerRecord[]> => {
    const response = await fetch(
      `/api/admin/daily-winners?date=${params.date}`,
    );

    if (!response.ok) {
      throw new Error("獲取每日中獎清單失敗");
    }

    return response.json();
  },
};

export type { WinnerRecord, DailyWinnersParams };
