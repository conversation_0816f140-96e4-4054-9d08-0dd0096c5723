"use client";
import React, { useState } from "react";
import type { MenuProps } from "antd";
import { Layout, Menu, theme } from "antd";
import { DailyReportPage } from "./components/DailyReportPage";
import { DrawResultPage } from "./components/DrawResultPage";
import { LuckyDrawRecordPage } from "./components/LuckyDrawRecordPage";
import { ConfigChancePage } from "./components/ConfigChancePage";
import { CouponManagePage } from "./components/CouponManagePage";
import "@ant-design/v5-patch-for-react-19";
import dynamic from "next/dynamic";

const { Header, Content, Sider } = Layout;

const items: MenuProps["items"] = [
  {
    key: "daily-report",
    label: "查詢每日中獎清單",
  },
  {
    key: "draw-result",
    label: "查詢抽獎紀錄",
  },
  {
    key: "lucky-draw-record",
    label: "查詢活動登錄名單",
  },
  {
    key: "coupon-manage",
    label: "獎券管理",
  },
  {
    key: "config-chance",
    label: "設定中獎機率",
  },
];

const App: React.FC = () => {
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  const [currentPage, setCurrentPage] = useState("daily-report");

  return (
    <Layout style={{ height: "100vh" }}>
      <Header style={{ display: "flex", alignItems: "center" }}>
        <div className="text-white">【威金森】- 超激夏祭活動後台</div>
      </Header>
      <Layout>
        <Sider width={200} style={{ background: colorBgContainer }}>
          <Menu
            items={items}
            onClick={({ key }) => {
              setCurrentPage(key);
            }}
          />
        </Sider>
        <Layout style={{ padding: "16px" }}>
          <Content
            className="p-6 min-h-[280px] overflow-auto"
            style={{
              padding: 24,
              margin: 0,
              minHeight: 280,
              background: colorBgContainer,
              borderRadius: borderRadiusLG,
            }}
          >
            {currentPage === "daily-report" && <DailyReportPage />}
            {currentPage === "draw-result" && <DrawResultPage />}
            {currentPage === "lucky-draw-record" && <LuckyDrawRecordPage />}
            {currentPage === "coupon-manage" && <CouponManagePage />}
            {currentPage === "config-chance" && <ConfigChancePage />}
          </Content>
        </Layout>
      </Layout>
    </Layout>
  );
};

export default dynamic(() => Promise.resolve(App), {
  ssr: false,
});
