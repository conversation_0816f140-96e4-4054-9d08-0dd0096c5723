import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { message } from "antd";
import { couponService, type CouponsParams, type CreateCouponsRequest } from "../services/couponService";

// Query Keys
export const couponKeys = {
  all: ["coupons"] as const,
  lists: () => [...couponKeys.all, "list"] as const,
  list: (params: CouponsParams) => [...couponKeys.lists(), params] as const,
};

// 獲取獎券列表的 hook
export const useCoupons = (params: CouponsParams) => {
  return useQuery({
    queryKey: couponKeys.list(params),
    queryFn: () => couponService.getCoupons(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (previously cacheTime)
  });
};

// 手動重新獲取獎券數據的 hook
export const useRefreshCoupons = () => {
  const queryClient = useQueryClient();

  return () => {
    queryClient.invalidateQueries({
      queryKey: couponKeys.lists(),
    });
  };
};

// 創建獎券的 mutation hook
export const useCreateCoupons = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateCouponsRequest) => couponService.createCoupons(data),
    onSuccess: (data) => {
      message.success(data.message);
      // 使所有獎券查詢失效，觸發重新獲取
      queryClient.invalidateQueries({
        queryKey: couponKeys.lists(),
      });
    },
    onError: (error: Error) => {
      message.error(error.message);
    },
  });
};
