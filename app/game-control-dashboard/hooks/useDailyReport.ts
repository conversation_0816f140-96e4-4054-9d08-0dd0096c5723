import { useQuery } from "@tanstack/react-query";
import {
  dailyReportService,
  type DailyWinnersParams,
} from "../services/dailyReportService";

// Query Keys
export const dailyReportKeys = {
  all: ["dailyReport"] as const,
  winners: () => [...dailyReportKeys.all, "winners"] as const,
  winnersByDate: (date: string) =>
    [...dailyReportKeys.winners(), date] as const,
};

// 獲取每日中獎清單的 hook
export const useDailyWinners = (params: DailyWinnersParams) => {
  return useQuery({
    queryKey: dailyReportKeys.winnersByDate(params.date),
    queryFn: () => dailyReportService.getDailyWinners(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
    enabled: !!params.date, // Only run query if date is provided
  });
};
