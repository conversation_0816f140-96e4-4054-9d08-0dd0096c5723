import { useQuery, useQueryClient } from "@tanstack/react-query";
import {
  drawResultService,
  type DrawResultsParams,
} from "../services/drawResultService";

// Query Keys
export const drawResultKeys = {
  all: ["drawResults"] as const,
  lists: () => [...drawResultKeys.all, "list"] as const,
  list: (params: DrawResultsParams) =>
    [...drawResultKeys.lists(), params] as const,
};

// 獲取抽獎結果列表的 hook
export const useDrawResults = (params: DrawResultsParams) => {
  return useQuery({
    queryKey: drawResultKeys.list(params),
    queryFn: () => drawResultService.getDrawResults(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    enabled: !!(params.startDate && params.endDate), // Only run if dates are provided
  });
};

// 手動重新獲取抽獎結果數據的 hook
export const useRefreshDrawResults = () => {
  const queryClient = useQueryClient();

  return () => {
    queryClient.invalidateQueries({
      queryKey: drawResultKeys.lists(),
    });
  };
};
