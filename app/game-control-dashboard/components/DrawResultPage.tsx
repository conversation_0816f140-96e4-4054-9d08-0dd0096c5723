"use client";
import React, { useState, useEffect, useCallback } from "react";
import {
  Table,
  DatePicker,
  Button,
  Space,
  message,
  Select,
  Tag,
  Input,
} from "antd";
import { DownloadOutlined, SearchOutlined } from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";

const { RangePicker } = DatePicker;
const { Option } = Select;
const { Search } = Input;

interface DrawRecord {
  id: string;
  userId: string;
  gameId: string;
  score: number;
  result: "win" | "lose";
  createdAt: string;
  user?: {
    nickname?: string;
    email?: string;
  };
  coupon?: {
    code: string;
  };
}

const columns: ColumnsType<DrawRecord> = [
  {
    title: "抽獎時間",
    dataIndex: "createdAt",
    key: "createdAt",
    render: (date: string) => dayjs(date).format("YYYY-MM-DD HH:mm:ss"),
    sorter: (a, b) => dayjs(a.createdAt).unix() - dayjs(b.createdAt).unix(),
  },
  {
    title: "用戶暱稱",
    dataIndex: ["user", "nickname"],
    key: "nickname",
    render: (nickname: string, record) =>
      nickname || record.user?.email || "未設定",
  },
  {
    title: "遊戲類型",
    dataIndex: "gameId",
    key: "gameId",
    render: (gameId: string) => {
      const gameNames = {
        balance: "極限平衡",
        catch: "威風接招",
        quiz: "威金森考驗",
      };
      return gameNames[gameId as keyof typeof gameNames] || gameId;
    },
  },
  {
    title: "遊戲分數",
    dataIndex: "score",
    key: "score",
    sorter: (a, b) => a.score - b.score,
  },
  {
    title: "抽獎結果",
    dataIndex: "result",
    key: "result",
    render: (result: string) => (
      <Tag color={result === "win" ? "green" : "red"}>
        {result === "win" ? "中獎" : "未中獎"}
      </Tag>
    ),
  },
  {
    title: "PIN碼",
    dataIndex: ["coupon", "code"],
    key: "couponCode",
    render: (code: string) => code || "-",
  },
];

export const DrawResultPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs, dayjs.Dayjs]>([
    dayjs().subtract(7, "day"),
    dayjs(),
  ]);
  const [gameFilter, setGameFilter] = useState<string>("all");
  const [resultFilter, setResultFilter] = useState<string>("all");
  const [searchText, setSearchText] = useState<string>("");
  const [allData, setAllData] = useState<DrawRecord[]>([]);

  // Filter data based on search text
  const filteredData = allData.filter((record) => {
    if (!searchText) return true;

    const searchLower = searchText.toLowerCase();
    const nickname = record.user?.nickname?.toLowerCase() || "";
    const email = record.user?.email?.toLowerCase() || "";
    const couponCode = record.coupon?.code?.toLowerCase() || "";

    return (
      nickname.includes(searchLower) ||
      email.includes(searchLower) ||
      couponCode.includes(searchLower)
    );
  });

  const fetchData = useCallback(async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        startDate: dateRange[0].format("YYYY-MM-DD"),
        endDate: dateRange[1].format("YYYY-MM-DD"),
        gameId: gameFilter === "all" ? "" : gameFilter,
        result: resultFilter === "all" ? "" : resultFilter,
      });

      const response = await fetch(`/api/admin/draw-records?${params}`);
      if (response.ok) {
        const result = await response.json();
        setAllData(result);
      } else {
        message.error("獲取數據失敗");
      }
    } catch (error) {
      console.error("Failed to fetch draw records:", error);
      message.error("網絡錯誤");
    } finally {
      setLoading(false);
    }
  }, [dateRange, gameFilter, resultFilter]);

  const exportToCSV = () => {
    const gameNames = {
      balance: "極限平衡",
      catch: "威風接招",
      quiz: "威金森考驗",
    };

    const csvContent = [
      ["抽獎時間", "用戶暱稱", "遊戲類型", "遊戲分數", "抽獎結果", "PIN碼"],
      ...filteredData.map((record) => [
        dayjs(record.createdAt).format("YYYY-MM-DD HH:mm:ss"),
        record.user?.nickname || record.user?.email || "未設定",
        gameNames[record.gameId as keyof typeof gameNames] || record.gameId,
        record.score.toString(),
        record.result === "win" ? "中獎" : "未中獎",
        record.coupon?.code || "-",
      ]),
    ]
      .map((row) => row.join(","))
      .join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute(
      "download",
      `draw-records-${dateRange[0].format("YYYY-MM-DD")}-${dateRange[1].format("YYYY-MM-DD")}.csv`,
    );
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return (
    <div className="h-full flex flex-col">
      <h2 className="text-xl font-bold mb-4">查詢抽獎紀錄</h2>

      <Space className="mb-4" wrap>
        <RangePicker
          value={dateRange}
          onChange={(dates) =>
            dates && setDateRange(dates as [dayjs.Dayjs, dayjs.Dayjs])
          }
          format="YYYY-MM-DD"
        />

        <Select
          value={gameFilter}
          onChange={setGameFilter}
          style={{ width: 120 }}
          placeholder="遊戲類型"
        >
          <Option value="all">全部遊戲</Option>
          <Option value="balance">極限平衡</Option>
          <Option value="catch">威風接招</Option>
          <Option value="quiz">威金森考驗</Option>
        </Select>

        <Select
          value={resultFilter}
          onChange={setResultFilter}
          style={{ width: 120 }}
          placeholder="抽獎結果"
        >
          <Option value="all">全部結果</Option>
          <Option value="win">中獎</Option>
          <Option value="lose">未中獎</Option>
        </Select>

        <Search
          placeholder="搜索用戶暱稱、Email 或 PIN碼"
          value={searchText}
          onChange={(e) => setSearchText(e.target.value)}
          style={{ width: 250 }}
          allowClear
        />

        <Button
          type="primary"
          icon={<SearchOutlined />}
          onClick={fetchData}
          loading={loading}
        >
          查詢
        </Button>

        <Button
          icon={<DownloadOutlined />}
          onClick={exportToCSV}
          disabled={filteredData.length === 0}
        >
          匯出CSV
        </Button>
      </Space>

      <div className="flex-1 overflow-hidden">
        <Table
          columns={columns}
          dataSource={filteredData}
          loading={loading}
          rowKey="id"
          scroll={{ x: 1200, y: "calc(100vh - 280px)" }}
          pagination={{
            pageSize: 20,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 筆記錄`,
          }}
        />
      </div>
    </div>
  );
};
