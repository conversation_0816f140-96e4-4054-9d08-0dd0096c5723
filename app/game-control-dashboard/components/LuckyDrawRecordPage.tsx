"use client";
import React, { useState, useEffect } from "react";
import { Table, Button, Space, message, Input, Tag } from "antd";
import { DownloadOutlined, SearchOutlined } from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";

const { Search } = Input;

interface UserRecord {
  id: string;
  nickname?: string;
  email?: string;
  luckyDrawName?: string;
  luckyDrawAge?: number;
  luckyDrawAddress?: string;
  luckyDrawPhone?: string;
  createdAt: string;
  gameRecords: {
    gameId: string;
    score: number;
    createdAt: string;
  }[];
  coupons: {
    code: string;
    createdAt: string;
  }[];
}

const columns: ColumnsType<UserRecord> = [
  {
    title: "註冊時間",
    dataIndex: "createdAt",
    key: "createdAt",
    render: (date: string) => dayjs(date).format("YYYY-MM-DD HH:mm:ss"),
    sorter: (a, b) => dayjs(a.createdAt).unix() - dayjs(b.createdAt).unix(),
  },
  {
    title: "用戶暱稱",
    dataIndex: "nickname",
    key: "nickname",
    render: (nickname: string, record) => nickname || record.email || "未設定",
  },
  {
    title: "抽獎姓名",
    dataIndex: "luckyDrawName",
    key: "luckyDrawName",
    render: (name: string) => name || "-",
  },
  {
    title: "年齡",
    dataIndex: "luckyDrawAge",
    key: "luckyDrawAge",
    render: (age: number) => age || "-",
  },
  {
    title: "聯絡電話",
    dataIndex: "luckyDrawPhone",
    key: "luckyDrawPhone",
    render: (phone: string) => phone || "-",
  },
  {
    title: "地址",
    dataIndex: "luckyDrawAddress",
    key: "luckyDrawAddress",
    render: (address: string) => address || "-",
    ellipsis: true,
  },
  {
    title: "遊戲次數",
    dataIndex: "gameRecords",
    key: "gameCount",
    render: (records: UserRecord["gameRecords"]) => records.length,
    sorter: (a, b) => a.gameRecords.length - b.gameRecords.length,
  },
  {
    title: "獲得PIN碼數",
    dataIndex: "coupons",
    key: "couponCount",
    render: (coupons: UserRecord["coupons"]) => (
      <Tag color={coupons.length > 0 ? "green" : "default"}>
        {coupons.length}
      </Tag>
    ),
    sorter: (a, b) => a.coupons.length - b.coupons.length,
  },
  {
    title: "資格狀態",
    key: "eligibility",
    render: (_, record) => {
      const hasAllGames = ["balance", "catch", "quiz"].every((gameId) =>
        record.gameRecords.some((gr) => gr.gameId === gameId),
      );
      return (
        <Tag color={hasAllGames ? "green" : "orange"}>
          {hasAllGames ? "符合抽獎資格" : "未完成全部遊戲"}
        </Tag>
      );
    },
  },
];

export const LuckyDrawRecordPage: React.FC = () => {
  const [data, setData] = useState<UserRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState("");
  const [filteredData, setFilteredData] = useState<UserRecord[]>([]);

  const fetchData = async () => {
    setLoading(true);
    try {
      const response = await fetch("/api/admin/user-records");
      if (response.ok) {
        const result = await response.json();
        setData(result);
        setFilteredData(result);
      } else {
        message.error("獲取數據失敗");
      }
    } catch (error) {
      console.error("Failed to fetch user records:", error);
      message.error("網絡錯誤");
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = (value: string) => {
    setSearchText(value);
    if (!value) {
      setFilteredData(data);
      return;
    }

    const filtered = data.filter(
      (record) =>
        record.nickname?.toLowerCase().includes(value.toLowerCase()) ||
        record.email?.toLowerCase().includes(value.toLowerCase()) ||
        record.luckyDrawName?.toLowerCase().includes(value.toLowerCase()) ||
        record.luckyDrawPhone?.includes(value),
    );
    setFilteredData(filtered);
  };

  const exportToCSV = () => {
    const csvContent = [
      [
        "註冊時間",
        "用戶暱稱",
        "Email",
        "抽獎姓名",
        "年齡",
        "聯絡電話",
        "地址",
        "遊戲次數",
        "獲得PIN碼數",
        "資格狀態",
      ],
      ...filteredData.map((record) => {
        const hasAllGames = ["balance", "catch", "quiz"].every((gameId) =>
          record.gameRecords.some((gr) => gr.gameId === gameId),
        );
        return [
          dayjs(record.createdAt).format("YYYY-MM-DD HH:mm:ss"),
          record.nickname || "未設定",
          record.email || "未設定",
          record.luckyDrawName || "-",
          record.luckyDrawAge?.toString() || "-",
          record.luckyDrawPhone || "-",
          record.luckyDrawAddress || "-",
          record.gameRecords.length.toString(),
          record.coupons.length.toString(),
          hasAllGames ? "符合抽獎資格" : "未完成全部遊戲",
        ];
      }),
    ]
      .map((row) => row.join(","))
      .join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute(
      "download",
      `user-records-${dayjs().format("YYYY-MM-DD")}.csv`,
    );
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  useEffect(() => {
    fetchData();
  }, []);

  const eligibleUsers = filteredData.filter((record) =>
    ["balance", "catch", "quiz"].every((gameId) =>
      record.gameRecords.some((gr) => gr.gameId === gameId),
    ),
  );

  return (
    <div>
      <h2 className="text-xl font-bold mb-4">查詢活動登錄名單</h2>

      <div className="mb-4">
        <div className="grid grid-cols-3 gap-4 mb-4 p-4 bg-gray-50 rounded">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {filteredData.length}
            </div>
            <div className="text-sm text-gray-600">總註冊人數</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {eligibleUsers.length}
            </div>
            <div className="text-sm text-gray-600">符合抽獎資格</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">
              {filteredData.reduce(
                (sum, record) => sum + record.coupons.length,
                0,
              )}
            </div>
            <div className="text-sm text-gray-600">總發放PIN碼數</div>
          </div>
        </div>
      </div>

      <Space className="mb-4">
        <Search
          placeholder="搜尋暱稱、Email、姓名或電話"
          allowClear
          enterButton={<SearchOutlined />}
          size="middle"
          onSearch={handleSearch}
          style={{ width: 300 }}
        />
        <Button type="primary" onClick={fetchData} loading={loading}>
          重新整理
        </Button>
        <Button
          icon={<DownloadOutlined />}
          onClick={exportToCSV}
          disabled={filteredData.length === 0}
        >
          匯出CSV
        </Button>
      </Space>

      <Table
        columns={columns}
        dataSource={filteredData}
        loading={loading}
        rowKey="id"
        pagination={{
          pageSize: 20,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 筆記錄`,
        }}
      />
    </div>
  );
};
