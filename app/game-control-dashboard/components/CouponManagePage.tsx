"use client";
import React, { useState, useEffect, useCallback } from "react";
import {
  Table,
  Button,
  Space,
  message,
  Input,
  Select,
  Tag,
  Modal,
  Form,
  Statistic,
  Row,
  Col,
  Card,
} from "antd";
import {
  PlusOutlined,
  SearchOutlined,
  DownloadOutlined,
  GiftOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
} from "@ant-design/icons";
import type { ColumnsType } from "antd/es/table";
import dayjs from "dayjs";

const { Search } = Input;
const { Option } = Select;
const { TextArea } = Input;

interface CouponRecord {
  id: string;
  code: string;
  createdAt: string;
  updatedAt: string;
  status: "available" | "assigned";
  user?: {
    id: string;
    nickname?: string;
    email?: string;
  };
}

interface PaginationInfo {
  current: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

interface StatsInfo {
  total: number;
  available: number;
  assigned: number;
}

interface ApiResponse {
  data: CouponRecord[];
  pagination: PaginationInfo;
  stats: StatsInfo;
}

const columns: ColumnsType<CouponRecord> = [
  {
    title: "獎券代碼",
    dataIndex: "code",
    key: "code",
    width: 150,
    render: (code: string) => <span className="font-mono text-sm">{code}</span>,
  },
  {
    title: "狀態",
    dataIndex: "status",
    key: "status",
    width: 100,
    render: (status: string) => {
      const statusConfig = {
        available: {
          color: "green",
          text: "可用",
          icon: <CheckCircleOutlined />,
        },
        assigned: {
          color: "orange",
          text: "已分配",
          icon: <ClockCircleOutlined />,
        },

      };
      const config = statusConfig[status as keyof typeof statusConfig];
      return (
        <Tag color={config.color} icon={config.icon}>
          {config.text}
        </Tag>
      );
    },
  },
  {
    title: "創建時間",
    dataIndex: "createdAt",
    key: "createdAt",
    width: 150,
    render: (date: string) => dayjs(date).format("YYYY-MM-DD HH:mm"),
    sorter: (a, b) => dayjs(a.createdAt).unix() - dayjs(b.createdAt).unix(),
  },
  {
    title: "分配給",
    dataIndex: "user",
    key: "user",
    width: 150,
    render: (user: CouponRecord["user"]) => {
      if (!user) return "-";
      return user.nickname || user.email || "未設定";
    },
  },

];

export const CouponManagePage: React.FC = () => {
  const [data, setData] = useState<CouponRecord[]>([]);
  const [loading, setLoading] = useState(false);
  const [searchText, setSearchText] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [pagination, setPagination] = useState<PaginationInfo>({
    current: 1,
    pageSize: 20,
    total: 0,
    totalPages: 0,
  });
  const [stats, setStats] = useState<StatsInfo>({
    total: 0,
    available: 0,
    assigned: 0,
  });
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();

  const fetchData = useCallback(
    async (page = 1, pageSize = 20) => {
      setLoading(true);
      try {
        const params = new URLSearchParams({
          page: page.toString(),
          pageSize: pageSize.toString(),
          search: searchText,
          status: statusFilter,
        });

        const response = await fetch(`/api/admin/coupons?${params}`);
        if (response.ok) {
          const result: ApiResponse = await response.json();
          setData(result.data);
          setPagination(result.pagination);
          setStats(result.stats);
        } else {
          message.error("獲取數據失敗");
        }
      } catch (error) {
        console.error("Failed to fetch coupons:", error);
        message.error("網絡錯誤");
      } finally {
        setLoading(false);
      }
    },
    [searchText, statusFilter],
  );

  const handleSearch = (value: string) => {
    setSearchText(value);
    setTimeout(() => {
      fetchData(1, pagination.pageSize);
    }, 300);
  };

  const handleStatusFilter = (value: string) => {
    setStatusFilter(value);
    fetchData(1, pagination.pageSize);
  };

  const handleAddCoupons = async (values: { coupons: string }) => {
    try {
      const couponCodes = values.coupons
        .split(/[\n,]/)
        .map((code) => code.trim())
        .filter((code) => code.length > 0);

      if (couponCodes.length === 0) {
        message.error("請輸入有效的獎券代碼");
        return;
      }

      const response = await fetch("/api/admin/coupons", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ coupons: couponCodes }),
      });

      const result = await response.json();

      if (response.ok) {
        message.success(result.message);
        setIsModalVisible(false);
        form.resetFields();
        fetchData(1, pagination.pageSize);
      } else {
        message.error(result.message);
      }
    } catch (error) {
      console.error("Failed to add coupons:", error);
      message.error("新增失敗");
    }
  };

  const exportToCSV = () => {
    const csvContent = [
      ["獎券代碼", "狀態", "創建時間", "分配給"],
      ...data.map((record) => [
        record.code,
        record.status === "available" ? "可用" : "已分配",
        dayjs(record.createdAt).format("YYYY-MM-DD HH:mm:ss"),
        record.user?.nickname || record.user?.email || "-",
      ]),
    ]
      .map((row) => row.join(","))
      .join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute(
      "download",
      `coupons-${dayjs().format("YYYY-MM-DD")}.csv`,
    );
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  return (
    <div className="h-full flex flex-col">
      <h2 className="text-xl font-bold mb-4">獎券管理</h2>

      {/* Statistics Cards */}
      <div className="mb-4">
        <Row gutter={16} className="mb-4">
          <Col span={6}>
            <Card>
              <Statistic
                title="總獎券數"
                value={stats.total}
                valueStyle={{ color: "#1677ff" }}
                prefix={<GiftOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="可用獎券"
                value={stats.available}
                valueStyle={{ color: "#52c41a" }}
                prefix={<CheckCircleOutlined />}
              />
            </Card>
          </Col>
          <Col span={6}>
            <Card>
              <Statistic
                title="已分配"
                value={stats.assigned}
                valueStyle={{ color: "#fa8c16" }}
                prefix={<ClockCircleOutlined />}
              />
            </Card>
          </Col>

        </Row>
      </div>

      {/* Controls */}
      <Space className="mb-4" wrap>
        <Search
          placeholder="搜尋獎券代碼或用戶"
          allowClear
          enterButton={<SearchOutlined />}
          size="middle"
          onSearch={handleSearch}
          style={{ width: 300 }}
        />

        <Select
          value={statusFilter}
          onChange={handleStatusFilter}
          style={{ width: 120 }}
        >
          <Option value="all">全部狀態</Option>
          <Option value="available">可用</Option>
          <Option value="assigned">已分配</Option>
        </Select>

        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => setIsModalVisible(true)}
        >
          新增獎券
        </Button>

        <Button
          type="primary"
          onClick={() => fetchData(1, pagination.pageSize)}
          loading={loading}
        >
          重新整理
        </Button>

        <Button
          icon={<DownloadOutlined />}
          onClick={exportToCSV}
          disabled={data.length === 0}
        >
          匯出CSV
        </Button>
      </Space>

      {/* Table */}

      <Table
        columns={columns}
        dataSource={data}
        loading={loading}
        rowKey="id"
        scroll={{ x: 800 }}
        pagination={{
          current: pagination.current,
          pageSize: pagination.pageSize,
          total: pagination.total,
          showSizeChanger: true,
          showQuickJumper: true,
          showTotal: (total) => `共 ${total} 筆記錄`,
          onChange: (page, pageSize) => {
            fetchData(page, pageSize);
          },
          onShowSizeChange: (_, size) => {
            fetchData(1, size);
          },
        }}
      />

      {/* Add Coupons Modal */}
      <Modal
        title="批量新增獎券"
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          form.resetFields();
        }}
        footer={null}
        width={600}
      >
        <Form form={form} layout="vertical" onFinish={handleAddCoupons}>
          <Form.Item
            name="coupons"
            label="獎券代碼"
            rules={[{ required: true, message: "請輸入獎券代碼" }]}
            extra="每行一個代碼，或用逗號分隔。系統會自動去除重複和無效的代碼。"
          >
            <TextArea
              rows={10}
              placeholder="請輸入獎券代碼，每行一個或用逗號分隔&#10;例如：&#10;COUPON001&#10;COUPON002&#10;COUPON003"
            />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                新增獎券
              </Button>
              <Button
                onClick={() => {
                  setIsModalVisible(false);
                  form.resetFields();
                }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};
