"use client";
import { imageUrl } from "@/utils/image-url";
import Image from "next/image";
import { PropsWithChildren } from "react";
import { MobileLayout } from "./mobile-layout";
import { useMedia, Media } from "@/utils/use-media";

export const WebsiteLayout = ({ children }: PropsWithChildren) => {
  const media = useMedia();

  if (media === Media.Desktop) {
    return (
      <Image
        unoptimized
        className="w-full h-full object-contain"
        alt=""
        src={imageUrl("/image-desktop.jpg")}
        width={1920}
        height={1080}
      />
    );
  }

  if (media === Media.Tablet) {
    return (
      <Image
        unoptimized
        className="w-full h-full object-contain"
        alt=""
        src={imageUrl("/image-tablet.jpg")}
        width={1024}
        height={1366}
      />
    );
  }

  return <MobileLayout>{children}</MobileLayout>;
};
