import { CSSProperties, PropsWithChildren } from "react";
import style from "./index.module.scss";
import { imageUrl } from "@/utils/image-url";
import clsx from "clsx";

export const TexturedText = ({
  children,
  className,
  color = "white",
}: PropsWithChildren<{ className?: string; color?: "yellow" | "white" }>) => {
  const backgroundImage =
    color === "yellow" ? "/text-texture-yellow.png" : "/text-texture-white.png";

  return (
    <span
      style={
        {
          "--text-texture": `url(${imageUrl(backgroundImage)})`,
        } as CSSProperties
      }
      className={clsx(
        style.texturedText,
        {
          ["text-[#ffff00]"]: color === "yellow",
          ["text-[#ffffff]"]: color === "white",
        },
        className,
      )}
    >
      {children}
    </span>
  );
};
