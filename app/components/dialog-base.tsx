import clsx from "clsx";
import { Mask } from "./mask";
import { PropsWithChildren } from "react";
import { createPortal } from "react-dom";

export const DialogBase = ({
  children,
  visible,
}: PropsWithChildren<{ visible: boolean }>) => {
  return createPortal(
    <div
      className={clsx(
        "fixed z-30 top-0 left-0 w-full h-full animate-fade-in",
        "flex justify-center items-center pt-[13vw]",
        { hidden: !visible },
      )}
    >
      <Mask visible />
      {children}
    </div>,
    document.body,
  );
};
