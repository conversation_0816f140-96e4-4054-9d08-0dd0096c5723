import { signIn } from "@/libs/auth";
import { imageUrl } from "@/utils/image-url";
import Image from "next/image";

async function actionLineLogin() {
  "use server";
  await signIn("line");
}

async function actionFacebookLogin() {
  "use server";
  await signIn("facebook");
}

export const LineLogin = () => {
  return (
    <button onClick={actionLineLogin} className="active:opacity-80">
      <Image
        unoptimized
        className="w-[51vw]"
        src={imageUrl("/login-line.png")}
        width={559}
        height={135}
        alt="Facebook login"
      />
    </button>
  );
};

export const FacebookLogin = () => {
  return (
    <button onClick={actionFacebookLogin} className="active:opacity-80">
      <Image
        unoptimized
        className="w-[51vw]"
        src={imageUrl("/login-facebook.png")}
        width={559}
        height={135}
        alt="Facebook login"
      />
    </button>
  );
};
