import clsx from "clsx";
import { PropsWithChildren } from "react";

export const ContentArea = ({
  children,
  className = "h-[116vw]",
}: PropsWithChildren<{ className?: string }>) => {
  return (
    <div
      className={clsx(
        className,
        "w-[76vw] bg-black flex flex-col items-center overflow-auto",
      )}
      style={{
        boxShadow:
          "0 0 0.6vw 0.3vw #ff0,0 0 0.6vw 0.3vw #ff0,0 0 0.6vw 0.3vw #ff0,0 0 3vw #ff0",
      }}
    >
      {children}
    </div>
  );
};
