import { FullScreenImage } from "@/app/components/full-screen";
import { FacebookLogin, LineLogin } from "@/app/components/login-buttons";
import { TexturedText } from "@/app/components/textured-text";
import { imageUrl } from "@/utils/image-url";

export const LoginScreen = () => {
  return (
    <>
      <FullScreenImage src={imageUrl("/screen-login.png")} />

      <div className="flex flex-col items-center relative pt-[56.5vw]">
        <TexturedText className="font-[1000] text-[5.5vw]">
          登入開始遊戲
        </TexturedText>

        <div className="flex flex-col items-center gap-[3vw] pt-[2vw]">
          <LineLogin />
          <FacebookLogin />
        </div>
      </div>
    </>
  );
};
