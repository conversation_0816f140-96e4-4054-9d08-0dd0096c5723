import { imageUrl } from "@/utils/image-url";
import { ButtonBase, ButtonBaseProps } from "./button-base";
import { TexturedText } from "../textured-text";

export const WideButton = (
  props: Omit<ButtonBaseProps, "sizeClass" | "backgroundImage" | "color">,
) => {
  const { children, ...restProps } = props;
  return (
    <ButtonBase
      sizeClass="w-[39vw] aspect-[4.3]"
      backgroundImage={{
        src: imageUrl("/button-wide.png"),
        width: 503,
        height: 143,
        sizeClass: "min-w-[110%]",
      }}
      {...restProps}
    >
      <TexturedText
        className="text-[4.8vw] font-[1000] textured-text"
        color="yellow"
      >
        {children}
      </TexturedText>
    </ButtonBase>
  );
};
