import { imageUrl } from "@/utils/image-url";
import { ButtonBase, ButtonBaseProps } from "./button-base";
import { TexturedText } from "../textured-text";

export const GameResultButton = (
  props: Omit<ButtonBaseProps, "sizeClass" | "backgroundImage" | "color">,
) => {
  const { children, ...restProps } = props;
  return (
    <ButtonBase
      sizeClass="w-[26vw] aspect-[2.3]"
      backgroundImage={{
        src: imageUrl("/button-buy.png"),
        width: 498,
        height: 297,
        sizeClass: "min-w-[140%]",
      }}
      {...restProps}
    >
      <TexturedText
        className="text-[4.2vw] font-[1000] textured-text"
        color="yellow"
      >
        {children}
      </TexturedText>
    </ButtonBase>
  );
};
