import { imageUrl } from "@/utils/image-url";
import { ButtonBase, ButtonBaseProps } from "./button-base";
import { TexturedText } from "../textured-text";

export const GameButton = (
  props: Omit<ButtonBaseProps, "sizeClass" | "backgroundImage" | "color">,
) => {
  const { children, ...restProps } = props;
  return (
    <ButtonBase
      sizeClass="w-[28vw] aspect-[3]"
      backgroundImage={{
        src: imageUrl("/button-app.png"),
        width: 569,
        height: 288,
        sizeClass: "min-w-[133%]",
      }}
      {...restProps}
    >
      <TexturedText
        className="text-[4.8vw] font-[1000] textured-text"
        color="yellow"
      >
        {children}
      </TexturedText>
    </ButtonBase>
  );
};
