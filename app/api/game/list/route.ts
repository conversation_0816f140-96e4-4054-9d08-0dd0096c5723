import { prisma } from "@/libs/prisma";

const query = () =>
  prisma.game.findMany({
    // filter by game start time after now
    where: { startTime: { lt: new Date() } },
    include: {
      // first game record
      GameRecord: {
        orderBy: { createdAt: "asc" },
        take: 1,
      },
    },
  });

export async function GET() {
  const res = await query();
  return Response.json(res);
}
