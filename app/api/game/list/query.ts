import { prisma } from "@/libs/prisma";

export const query = (userId: string) =>
  prisma.game.findMany({
    // filter by game start time after now
    where: { startTime: { lt: new Date() } },
    include: {
      // first game record belong to the user
      GameRecord: {
        where: { userId },
        orderBy: { createdAt: "asc" },
        take: 1,
      },
    },
  });

export type GameList = Awaited<ReturnType<typeof query>>;
