import { auth } from "@/libs/auth";
import { prisma } from "@/libs/prisma";
import { NextResponse } from "next/server";

export async function GET() {
  const session = await auth();
  const userRole = session?.user.systemRole;

  if (userRole !== "ADMIN" && userRole !== "DEVELOPER") {
    return NextResponse.json({ message: "沒有權限" }, { status: 403 });
  }

  try {
    const users = await prisma.user.findMany({
      select: {
        id: true,
        nickname: true,
        email: true,
        luckyDrawName: true,
        luckyDrawAge: true,
        luckyDrawAddress: true,
        luckyDrawPhone: true,
        createdAt: true,
        GameRecord: {
          select: {
            gameId: true,
            score: true,
            createdAt: true,
          },
          orderBy: {
            createdAt: "desc",
          },
        },
        Coupon: {
          select: {
            code: true,
            createdAt: true,
          },
          orderBy: {
            createdAt: "desc",
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    const formattedUsers = users.map((user) => ({
      id: user.id,
      nickname: user.nickname,
      email: user.email,
      luckyDrawName: user.luckyDrawName,
      luckyDrawAge: user.luckyDrawAge,
      luckyDrawAddress: user.luckyDrawAddress,
      luckyDrawPhone: user.luckyDrawPhone,
      createdAt: user.createdAt.toISOString(),
      gameRecords: user.GameRecord.map((record) => ({
        gameId: record.gameId,
        score: Number(record.score),
        createdAt: record.createdAt.toISOString(),
      })),
      coupons: user.Coupon.map((coupon) => ({
        code: coupon.code,
        createdAt: coupon.createdAt.toISOString(),
      })),
    }));

    return NextResponse.json(formattedUsers);
  } catch (error) {
    console.error("Error fetching user records:", error);
    return NextResponse.json({ message: "伺服器錯誤" }, { status: 500 });
  }
}
