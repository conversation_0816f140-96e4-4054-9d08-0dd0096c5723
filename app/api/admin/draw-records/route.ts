import { auth } from "@/libs/auth";
import { prisma } from "@/libs/prisma";
import { NextRequest, NextResponse } from "next/server";

export async function GET(req: NextRequest) {
  const session = await auth();
  const userRole = session?.user.systemRole;

  if (userRole !== "ADMIN" && userRole !== "DEVELOPER") {
    return NextResponse.json({ message: "沒有權限" }, { status: 403 });
  }

  const startDate = req.nextUrl.searchParams.get("startDate");
  const endDate = req.nextUrl.searchParams.get("endDate");
  const gameId = req.nextUrl.searchParams.get("gameId");
  const result = req.nextUrl.searchParams.get("result");

  if (!startDate || !endDate) {
    return NextResponse.json({ message: "缺少日期參數" }, { status: 400 });
  }

  try {
    // Use actual draw results from GameRecord table
    const gameRecords = await prisma.gameRecord.findMany({
      where: {
        createdAt: {
          gte: new Date(startDate),
          lte: new Date(endDate + "T23:59:59.999Z"),
        },
        gameId: gameId || undefined,
        // Only include records that have participated in draw
        drawResult: result ? (result === "win" ? "WIN" : "LOSE") : undefined,
      },
      include: {
        user: {
          select: {
            nickname: true,
            email: true,
          },
        },
        coupon: {
          select: {
            code: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
    });

    // Map to the expected format
    const drawRecords = gameRecords
      .filter((record) => record.drawResult !== null) // Only include records with draw results
      .map((record) => ({
        id: record.id,
        userId: record.userId,
        gameId: record.gameId,
        score: Number(record.score),
        result: record.drawResult === "WIN" ? "win" : "lose",
        createdAt: record.createdAt.toISOString(),
        user: record.user,
        coupon: record.coupon,
      }));

    return NextResponse.json(drawRecords);
  } catch (error) {
    console.error("Error fetching draw records:", error);
    return NextResponse.json({ message: "伺服器錯誤" }, { status: 500 });
  }
}
