import { auth } from "@/libs/auth";
import { prisma } from "@/libs/prisma";
import { NextRequest, NextResponse } from "next/server";

export async function GET(req: NextRequest) {
  const session = await auth();
  const userRole = session?.user.systemRole;

  if (userRole !== "ADMIN" && userRole !== "DEVELOPER") {
    return NextResponse.json({ message: "沒有權限" }, { status: 403 });
  }

  const startDate = req.nextUrl.searchParams.get("startDate");
  const endDate = req.nextUrl.searchParams.get("endDate");
  const gameId = req.nextUrl.searchParams.get("gameId");
  const result = req.nextUrl.searchParams.get("result");
  const page = parseInt(req.nextUrl.searchParams.get("page") || "1");
  const pageSize = parseInt(req.nextUrl.searchParams.get("pageSize") || "20");
  const search = req.nextUrl.searchParams.get("search") || "";

  if (!startDate || !endDate) {
    return NextResponse.json({ message: "缺少日期參數" }, { status: 400 });
  }

  try {
    // Build where clause for search
    const whereClause: any = {
      createdAt: {
        gte: new Date(startDate),
        lte: new Date(endDate + "T23:59:59.999Z"),
      },
      gameId: gameId || undefined,
      // Only include records that have participated in draw
      drawResult: result ? (result === "win" ? "WIN" : "LOSE") : { not: null },
    };

    // Add search conditions if search term is provided
    if (search) {
      whereClause.OR = [
        {
          user: {
            nickname: {
              contains: search,
              mode: "insensitive",
            },
          },
        },
        {
          user: {
            email: {
              contains: search,
              mode: "insensitive",
            },
          },
        },
        {
          coupon: {
            code: {
              contains: search,
              mode: "insensitive",
            },
          },
        },
      ];
    }

    // Get total count for pagination
    const totalCount = await prisma.gameRecord.count({
      where: whereClause,
    });

    // Calculate pagination
    const skip = (page - 1) * pageSize;
    const totalPages = Math.ceil(totalCount / pageSize);

    // Use actual draw results from GameRecord table
    const gameRecords = await prisma.gameRecord.findMany({
      where: whereClause,
      include: {
        user: {
          select: {
            nickname: true,
            email: true,
          },
        },
        coupon: {
          select: {
            code: true,
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      skip,
      take: pageSize,
    });

    // Map to the expected format
    const drawRecords = gameRecords.map((record) => ({
      id: record.id,
      userId: record.userId,
      gameId: record.gameId,
      score: Number(record.score),
      result: record.drawResult === "WIN" ? "win" : "lose",
      createdAt: record.createdAt.toISOString(),
      user: record.user,
      coupon: record.coupon,
    }));

    return NextResponse.json({
      data: drawRecords,
      pagination: {
        current: page,
        pageSize,
        total: totalCount,
        totalPages,
      },
    });
  } catch (error) {
    console.error("Error fetching draw records:", error);
    return NextResponse.json({ message: "伺服器錯誤" }, { status: 500 });
  }
}
