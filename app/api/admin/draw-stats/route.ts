import { auth } from "@/libs/auth";
import { prisma } from "@/libs/prisma";
import { NextResponse } from "next/server";

export async function GET() {
  const session = await auth();
  const userRole = session?.user.systemRole;

  if (userRole !== "ADMIN" && userRole !== "DEVELOPER") {
    return NextResponse.json({ message: "沒有權限" }, { status: 403 });
  }

  try {
    // Get total users count
    const totalUsers = await prisma.user.count();

    // Get total game records (representing draw attempts)
    const totalDraws = await prisma.gameRecord.count();

    // Get total wins (coupons that have been assigned to users)
    const totalWins = await prisma.coupon.count({
      where: {
        userId: {
          not: null,
        },
      },
    });

    // Calculate actual win rate
    const actualWinRate = totalDraws > 0 ? (totalWins / totalDraws) * 100 : 0;

    const stats = {
      totalUsers,
      totalDraws,
      totalWins,
      actualWinRate: Math.round(actualWinRate * 100) / 100, // Round to 2 decimal places
    };

    return NextResponse.json(stats);
  } catch (error) {
    console.error("Error fetching draw stats:", error);
    return NextResponse.json({ message: "伺服器錯誤" }, { status: 500 });
  }
}
