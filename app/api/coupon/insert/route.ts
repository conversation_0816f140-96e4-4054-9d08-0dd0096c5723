import { auth } from "@/libs/auth";
import { NextRequest, NextResponse } from "next/server";
import { query } from "./query";

export async function POST(req: NextRequest) {
  const session = await auth();
  const userRole = session?.user.systemRole;

  if (userRole !== "ADMIN" && userRole !== "DEVELOPER") {
    return NextResponse.json({ message: "沒有權限" }, { status: 403 });
  }

  try {
    const body = await req.json();
    const { coupons } = body;

    if (!coupons || !Array.isArray(coupons) || coupons.length === 0) {
      return NextResponse.json({ message: "請提供有效的獎券代碼列表" }, { status: 400 });
    }

    // Validate and clean coupon codes
    const validCoupons = coupons.filter((code) =>
      typeof code === "string" && code.trim().length > 0
    ).map((code) => code.trim());

    if (validCoupons.length === 0) {
      return NextResponse.json({ message: "沒有有效的獎券代碼" }, { status: 400 });
    }

    await query(validCoupons);

    return NextResponse.json({
      message: `成功新增 ${validCoupons.length} 張獎券`,
      count: validCoupons.length,
    });
  } catch (error) {
    console.error("Error creating coupons:", error);
    return NextResponse.json({ message: "伺服器錯誤" }, { status: 500 });
  }
}
