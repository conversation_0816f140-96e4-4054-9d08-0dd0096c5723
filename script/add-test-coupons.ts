import { prisma } from "@/libs/prisma";

async function addTestCoupons() {
  const testCoupons = [
    "TEST001",
    "TEST002", 
    "TEST003",
    "TEST004",
    "TEST005",
    "WIN001",
    "WIN002",
    "WIN003",
    "LUCKY001",
    "LUCKY002",
  ];

  try {
    const result = await prisma.coupon.createMany({
      data: testCoupons.map((code) => ({ code })),
      skipDuplicates: true,
    });

    console.log(`Successfully created ${result.count} test coupons`);
  } catch (error) {
    console.error("Error creating test coupons:", error);
  } finally {
    await prisma.$disconnect();
  }
}

addTestCoupons();
