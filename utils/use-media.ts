"use client";

import { useEffect, useState } from "react";

export enum Media {
  Mobile = "mobile",
  Tablet = "tablet",
  Desktop = "desktop",
}

const getMedia = () => {
  if (typeof window !== "undefined") {
    if (window.innerWidth >= 768) {
      if (window.innerHeight > window.innerWidth) {
        return Media.Tablet;
      } else {
        return Media.Desktop;
      }
    }
  }

  return Media.Mobile;
};

export const useMedia = () => {
  const [media, setMedia] = useState<Media>(getMedia());

  useEffect(() => {
    const onResize = () => {
      setMedia(getMedia());
    };
    window.addEventListener("resize", onResize);
    return () => window.removeEventListener("resize", onResize);
  }, []);

  return media;
};
