# 抽獎 API 結果處理實現

## 修復內容

我已經修復了抽獎 API 結果處理中的問題，確保前端能正確使用抽獎 API 的結果來設定抽獎狀態。

## 主要修復

### 1. 前端請求修復 (`app/(home)/(authorized)/game/game-result.tsx`)

#### 添加 Content-Type 標頭
```typescript
const response = await fetch("/api/game/draw", {
  method: "POST",
  headers: {
    "Content-Type": "application/json", // 添加必要的標頭
  },
  body: JSON.stringify({ gameRecordId }),
});
```

#### 修正結果判斷邏輯
```typescript
if (response.ok) {
  const result = await response.json();
  setDrawResult(
    result.result === "win" ? DrawResult.Win : DrawResult.Lose, // 使用小寫 "win"
  );
  if (result.coupon) {
    setCouponCode(result.coupon.code);
  }
}
```

### 2. 後端 API 修復 (`app/api/game/draw/route.ts`)

#### 統一返回格式
```typescript
return NextResponse.json({
  result: isWin ? "win" : "lose", // 返回小寫格式，與前端一致
  coupon: updatedGameRecord.coupon,
});
```

## API 響應格式

### 成功響應 (中獎)
```json
{
  "result": "win",
  "coupon": {
    "code": "WIN12345678"
  }
}
```

### 成功響應 (未中獎)
```json
{
  "result": "lose",
  "coupon": null
}
```

### 錯誤響應
```json
{
  "message": "已經抽過獎了"
}
```

## 前端狀態設定流程

1. **發送請求**: 使用正確的 Content-Type 標頭
2. **檢查響應**: 驗證 HTTP 狀態碼
3. **解析結果**: 獲取 result 和 coupon 數據
4. **設定狀態**: 
   - `setDrawResult()` 設定抽獎結果顯示
   - `setCouponCode()` 設定中獎代碼
5. **錯誤處理**: 顯示錯誤訊息

## 完整的抽獎流程

```typescript
const performDraw = async () => {
  if (!gameRecordId) {
    alert("無法進行抽獎，請重新遊戲");
    return;
  }

  setIsDrawing(true);
  try {
    // 1. 發送抽獎請求
    const response = await fetch("/api/game/draw", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ gameRecordId }),
    });

    if (response.ok) {
      // 2. 解析響應
      const result = await response.json();
      
      // 3. 設定抽獎結果
      setDrawResult(
        result.result === "win" ? DrawResult.Win : DrawResult.Lose,
      );
      
      // 4. 設定中獎代碼（如果有）
      if (result.coupon) {
        setCouponCode(result.coupon.code);
      }
    } else {
      // 5. 處理錯誤
      const error = await response.json();
      alert(error.message || "抽獎失敗");
    }
  } catch (error) {
    console.error("Draw error:", error);
    alert("網絡錯誤，請稍後再試");
  } finally {
    setIsDrawing(false);
  }
};
```

## 數據一致性保證

1. **格式統一**: API 返回小寫 "win"/"lose"，前端檢查小寫格式
2. **類型安全**: 使用 TypeScript 確保類型正確
3. **錯誤處理**: 完整的錯誤處理機制
4. **狀態管理**: 正確的狀態設定和重置

## 測試驗證

### 測試中獎情況
1. 確保有可用的 coupon 在數據庫中
2. 調整 `luckyDrawChance` 為較高值（如 1.0）
3. 完成遊戲並抽獎
4. 驗證：
   - 顯示中獎對話框
   - 顯示正確的 PIN 碼
   - 數據庫中 GameRecord 的 drawResult 為 "WIN"
   - 數據庫中 coupon 被正確分配

### 測試未中獎情況
1. 調整 `luckyDrawChance` 為較低值（如 0.0）
2. 完成遊戲並抽獎
3. 驗證：
   - 顯示未中獎對話框
   - 數據庫中 GameRecord 的 drawResult 為 "LOSE"
   - 沒有 coupon 被分配

### 測試錯誤情況
1. 重複抽獎：應該顯示 "已經抽過獎了"
2. 無效 gameRecordId：應該顯示相應錯誤
3. 網絡錯誤：應該顯示 "網絡錯誤，請稍後再試"

## 優勢

1. **數據一致性**: 前後端使用統一的格式
2. **錯誤處理**: 完善的錯誤處理和用戶反饋
3. **狀態管理**: 正確的狀態設定和重置
4. **用戶體驗**: 清晰的抽獎進度和結果顯示
5. **類型安全**: TypeScript 提供類型檢查
