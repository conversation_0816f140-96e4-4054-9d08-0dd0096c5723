# 抽獎系統測試指南

## 已實現的功能

### 1. 真實抽獎邏輯
- **API 端點**: `/api/game/draw`
- **功能**: 基於 WebsiteConfig 中的 luckyDrawChance 進行真實抽獎
- **數據存儲**: 將抽獎結果存儲到 GameRecord 的 drawResult 和 couponId 字段

### 2. 抽獎記錄查詢
- **API 端點**: `/api/admin/draw-records`
- **功能**: 使用真實的 DrawResult 和 couponId 數據進行查詢
- **過濾**: 支持按日期、遊戲類型、抽獎結果過濾

### 3. 前端抽獎界面
- **組件**: `GameResult`
- **功能**: 調用真實抽獎 API，顯示真實結果
- **狀態管理**: 防止重複抽獎，顯示抽獎進度

### 4. 增強的查詢界面
- **組件**: `DrawResultPage`
- **功能**: 
  - 實時搜索用戶暱稱、Email、PIN碼
  - 客戶端過濾
  - CSV 導出

### 5. 用戶 Coupon 查詢
- **API 端點**: `/api/coupon/my`
- **功能**: 查詢用戶獲得的所有 coupon

## 測試步驟

### 1. 測試抽獎功能
1. 完成一個遊戲，獲得 gameRecordId
2. 點擊「分享立即抽」按鈕
3. 驗證抽獎結果是否正確顯示
4. 檢查數據庫中 GameRecord 的 drawResult 和 couponId 字段

### 2. 測試查詢功能
1. 訪問管理後台的抽獎記錄頁面
2. 測試日期範圍過濾
3. 測試遊戲類型過濾
4. 測試抽獎結果過濾
5. 測試搜索功能（用戶名、Email、PIN碼）

### 3. 測試數據一致性
1. 驗證抽獎記錄與實際 GameRecord 數據一致
2. 驗證 coupon 分配正確
3. 驗證中獎率符合配置

## 數據庫結構

### GameRecord 表新增字段
- `drawResult`: DrawResult enum (WIN/LOSE)
- `couponId`: String (關聯到 Coupon 表)

### 關聯關係
- GameRecord -> Coupon (通過 couponId)
- Coupon -> User (通過 userId)

## API 端點總結

1. `POST /api/game/draw` - 執行抽獎
2. `GET /api/admin/draw-records` - 查詢抽獎記錄
3. `GET /api/coupon/my` - 查詢用戶 coupon
4. `GET /api/record/new` - 創建遊戲記錄（返回 gameRecordId）

## 注意事項

1. 確保有足夠的 coupon 庫存用於中獎分配
2. 抽獎機率在 WebsiteConfig 中配置
3. 每個 GameRecord 只能抽獎一次
4. 中獎時會自動分配可用的 coupon
